/**
 * 🔧 [类型定义] 后端API类型定义
 * 与后端schemas完全匹配的TypeScript类型定义
 */

// ===== 枚举类型定义 =====

/**
 * AI服务提供商枚举 - 与backend/app/schemas/generation.py中的AIProvider匹配
 */
export enum AIProvider {
  ZHIPU = "zhipu",
  KIMI = "kimi"
}

/**
 * 生成状态枚举 - 与backend/app/schemas/generation.py中的GenerationStatus匹配
 */
export enum GenerationStatus {
  PENDING = "pending",      // 等待中
  GENERATING = "generating", // 生成中
  COMPLETED = "completed",   // 已完成
  FAILED = "failed"         // 失败
}

/**
 * 小说类型枚举 - 与backend/app/schemas/generation.py中的StoryGenre匹配
 */
export enum StoryGenre {
  ROMANCE = "romance",           // 言情
  FANTASY = "fantasy",           // 奇幻
  URBAN = "urban",              // 都市
  HISTORICAL = "historical",     // 历史
  MYSTERY = "mystery",          // 悬疑
  SCIFI = "scifi",              // 科幻
  MARTIAL_ARTS = "martial_arts", // 武侠
  THRILLER = "thriller"         // 惊悚
}

// ===== 请求类型定义 =====

/**
 * 故事圣经生成请求 - 与backend/app/schemas/generation.py中的StoryBibleRequest匹配
 */
export interface StoryBibleRequest {
  title: string;                    // 小说标题 (1-100字符)
  genre: StoryGenre;               // 小说类型
  theme: string;                   // 小说主题 (10-500字符)
  protagonist: string;             // 主角设定 (10-300字符)
  setting: string;                 // 故事背景 (10-500字符)
  plot_outline: string;            // 情节大纲 (20-1000字符)
  target_audience?: string;        // 目标读者群体 (可选，最多100字符)
  writing_style?: string;          // 写作风格要求 (可选，最多200字符)
  ai_provider: AIProvider;         // AI服务提供商
  temperature?: number;            // 生成温度参数 (默认0.8)
  max_tokens?: number;             // 最大生成token数 (默认3000)
}

/**
 * 章节生成请求 - 与backend/app/schemas/generation.py中的ChapterGenerationRequest匹配
 */
export interface ChapterGenerationRequest {
  story_bible_id: string;          // 故事圣经ID
  chapter_number: number;          // 章节号 (>=1)
  chapter_title: string;           // 章节标题 (1-100字符)
  chapter_outline: string;         // 章节大纲 (10-500字符)
  previous_chapter_summary?: string; // 前一章节摘要 (可选，最多300字符)
  character_development?: string;   // 角色发展要求 (可选，最多300字符)
  plot_requirements?: string;       // 情节要求 (可选，最多300字符)
  target_word_count?: number;       // 目标字数 (默认2000)
  ai_provider: AIProvider;         // AI服务提供商
  temperature?: number;            // 生成温度参数 (默认0.8)
  max_tokens?: number;             // 最大生成token数 (默认4000)
}

// ===== 响应类型定义 =====

/**
 * 生成任务基础响应 - 与backend/app/schemas/generation.py中的GenerationTaskBase匹配
 */
export interface GenerationTaskBase {
  id: string;                      // 任务ID
  task_type: string;               // 任务类型 (story_bible, chapter)
  status: GenerationStatus;        // 生成状态
  ai_provider: AIProvider;         // AI服务提供商
  created_at: string;              // 创建时间 (ISO格式)
  updated_at: string;              // 更新时间 (ISO格式)
  error_message?: string;          // 错误信息 (可选)
}

/**
 * 故事圣经响应 - 与backend/app/schemas/generation.py中的StoryBibleResponse匹配
 */
export interface StoryBibleResponse extends GenerationTaskBase {
  task_type: "story_bible";
  request_data: StoryBibleRequest; // 请求数据
  
  // 生成结果
  generated_content?: string;      // 生成的故事圣经内容
  character_profiles?: Array<{[key: string]: any}>; // 角色档案
  world_building?: string;         // 世界观设定
  plot_structure?: string;         // 情节结构
  writing_guidelines?: string;     // 写作指南
  
  // 生成统计
  generation_stats?: {[key: string]: any}; // 生成统计信息
}

/**
 * 章节响应 - 与backend/app/schemas/generation.py中的ChapterResponse匹配
 */
export interface ChapterResponse extends GenerationTaskBase {
  task_type: "chapter";
  request_data: ChapterGenerationRequest; // 请求数据
  
  // 生成结果
  generated_content?: string;      // 生成的章节内容
  chapter_summary?: string;        // 章节摘要
  character_development_notes?: string; // 角色发展记录
  plot_advancement?: string;       // 情节推进记录
  
  // 生成统计
  generation_stats?: {[key: string]: any}; // 生成统计信息
}

// ===== 系统响应类型 =====

/**
 * 健康检查响应 - 与backend/main.py中的health_check匹配
 */
export interface HealthCheckResponse {
  status: "healthy";
  message: string;
  version: string;
  service: string;
  ai_providers: {
    default: string;
    available: string[];
  };
  timestamp?: string;
}

/**
 * 根路径响应 - 与backend/main.py中的root匹配
 */
export interface RootResponse {
  message: string;
  service: string;
  version: string;
  docs: string;
  health: string;
  description: string;
}

// ===== 任务管理类型 =====

/**
 * 任务状态查询响应
 */
export interface TaskStatusResponse extends GenerationTaskBase {
  progress?: number;               // 进度百分比 (0-100)
  current_step?: string;           // 当前步骤描述
  estimated_completion?: string;   // 预计完成时间
}

/**
 * 任务列表响应
 */
export interface TaskListResponse {
  tasks: TaskStatusResponse[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * 章节列表响应
 */
export interface ChapterListResponse {
  chapters: ChapterResponse[];
  total: number;
  limit: number;
  offset: number;
  bible_id: string;
}

// ===== 错误响应类型 =====

/**
 * API错误响应
 */
export interface APIErrorResponse {
  detail: string;
  status_code: number;
  error_type?: string;
  timestamp?: string;
}

// ===== 工具类型 =====

/**
 * API响应包装器 - 统一的API响应格式
 */
export type APIResponse<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: APIErrorResponse;
};

/**
 * 分页参数
 */
export interface PaginationParams {
  limit?: number;    // 每页数量 (1-100，默认20)
  offset?: number;   // 偏移量 (>=0，默认0)
}

/**
 * 筛选参数
 */
export interface FilterParams {
  status?: GenerationStatus;  // 状态筛选
  ai_provider?: AIProvider;   // AI提供商筛选
  genre?: StoryGenre;         // 类型筛选
}
