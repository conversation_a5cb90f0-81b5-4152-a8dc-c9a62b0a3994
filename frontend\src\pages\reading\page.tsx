// 章节阅读页面组件 - 参考番茄小说布局
import { useState, useEffect } from 'react';
import { useNotification } from '../../components/NotificationProvider';
import { useTheme } from '../../components/ThemeProvider';
import type { StoryBible, Chapter } from '../../stores/storyStore';

interface ChapterReadingPageProps {
  storyBible: StoryBible;
  currentChapter: Chapter;
  allChapters: Chapter[];
  selectedAIProvider?: string | null | undefined;
  onGenerateNextChapter: () => void;
  onBackToWriting: () => void;
  isGenerating: boolean;
}

export const ChapterReadingPage: React.FC<ChapterReadingPageProps> = ({
  storyBible,
  currentChapter,
  allChapters,
  selectedAIProvider,
  onGenerateNextChapter,
  onBackToWriting,
  isGenerating
}) => {
  const { isDark: darkMode, fontSize, setFontSize, toggleDarkMode } = useTheme();
  const [showSettings, setShowSettings] = useState(false);
  const { showNotification } = useNotification();

  // 页面位置日志
  useEffect(() => {
    console.log('📍 [页面位置] 当前位置: 章节阅读页面 (ChapterReadingPage)');
    console.log('🌙 [ChapterReadingPage] 夜间模式状态:', darkMode);
  }, [darkMode]);

  const getProviderDisplayName = (provider: string | null | undefined): string => {
    if (!provider) return '智能选择';
    const names: Record<string, string> = {
      'zhipu': '智谱AI',
      'kimi': 'Kimi',
      'openai': 'OpenAI',
      'claude': 'Claude'
    };
    return names[provider] || provider;
  };

  const currentIndex = allChapters.findIndex(ch => ch.id === currentChapter.id);
  const hasPrevious = currentIndex > 0;
  const hasNext = currentIndex < allChapters.length - 1;

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      darkMode 
        ? 'bg-gray-900 text-gray-100' 
        : 'bg-white text-gray-900'
    }`}>
      {/* 顶部导航栏 */}
      <header className={`sticky top-0 z-50 border-b transition-colors duration-300 ${
        darkMode 
          ? 'bg-gray-800 border-gray-600' 
          : 'bg-white border-gray-200'
      }`}>
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            {/* 左侧：返回按钮和标题 */}
            <div className="flex items-center space-x-4">
              <button
                onClick={onBackToWriting}
                className={`p-2 rounded-full transition-colors ${
                  darkMode 
                    ? 'hover:bg-gray-700 text-gray-300' 
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div>
                <h1 className="font-bold text-lg truncate max-w-xs">
                  {storyBible.request_data.title}
                </h1>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  第{currentChapter.request_data.chapter_number}章
                  {currentChapter.request_data.chapter_title && ` - ${currentChapter.request_data.chapter_title}`}
                </p>
              </div>
            </div>

            {/* 右侧：设置按钮 */}
            <div className="flex items-center space-x-2">
              {/* 日夜间模式切换 */}
              <button
                onClick={() => {
                      toggleDarkMode();
                      showNotification({
                        type: 'success',
                        title: `已切换到${!darkMode ? '夜间' : '日间'}模式`,
                        message: ''
                      });
                    }}
                className={`p-2 rounded-full transition-colors ${
                  darkMode 
                    ? 'hover:bg-gray-700 text-yellow-400' 
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
                title={darkMode ? '切换到日间模式' : '切换到夜间模式'}
              >
                {darkMode ? (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path fillRule="evenodd" d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z" clipRule="evenodd" />
                  </svg>
                )}
              </button>

              {/* 设置按钮 */}
              <button
                onClick={() => setShowSettings(!showSettings)}
                className={`p-2 rounded-full transition-colors ${
                  darkMode 
                    ? 'hover:bg-gray-700 text-gray-300' 
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
            </div>
          </div>

          {/* 设置面板 */}
          {showSettings && (
            <div className={`mt-4 p-4 rounded-lg border transition-colors ${
              darkMode 
                ? 'bg-gray-700 border-gray-600' 
                : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center justify-between">
                <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  字体大小
                </span>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setFontSize(Math.max(14, fontSize - 2))}
                    className={`px-2 py-1 rounded transition-colors ${
                      darkMode 
                        ? 'bg-gray-600 hover:bg-gray-500 text-gray-300' 
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                    }`}
                  >
                    A-
                  </button>
                  <span className={`text-sm min-w-8 text-center ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {fontSize}
                  </span>
                  <button
                    onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                    className={`px-2 py-1 rounded transition-colors ${
                      darkMode 
                        ? 'bg-gray-600 hover:bg-gray-500 text-gray-300' 
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                    }`}
                  >
                    A+
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* 主内容区域 */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        {/* 章节标题 */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold mb-2">
            第{currentChapter.request_data.chapter_number}章
            {currentChapter.request_data.chapter_title && ` ${currentChapter.request_data.chapter_title}`}
          </h1>
          {currentChapter.request_data.chapter_outline && (
            <div className={`inline-block px-4 py-2 rounded-lg text-sm ${
              darkMode
                ? 'bg-gray-800 text-gray-400 border border-gray-700'
                : 'bg-blue-50 text-blue-600 border border-blue-200'
            }`}>
              {currentChapter.request_data.chapter_outline}
            </div>
          )}
        </div>

        {/* 章节正文 */}
        <div
          className={`prose prose-lg max-w-none leading-relaxed transition-colors ${
            darkMode ? 'prose-invert' : ''
          }`}
          style={{ fontSize: `${fontSize}px`, lineHeight: '1.8' }}
        >
          <div className="whitespace-pre-wrap font-serif">
            {currentChapter.generated_content || '章节内容生成中...'}
          </div>
        </div>

        {/* 章节信息 */}
        <div className={`mt-8 pt-6 border-t ${
          darkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className={`flex flex-wrap items-center gap-4 text-sm ${
            darkMode ? 'text-gray-400' : 'text-gray-500'
          }`}>
            <span>生成模型: {currentChapter.ai_provider}</span>
            <span>Token消耗: {currentChapter.generation_stats?.total_tokens || 'N/A'}</span>
            <span>创建时间: {new Date(currentChapter.created_at).toLocaleString()}</span>
            <span>字数: {(currentChapter.generated_content || '').length.toLocaleString()}</span>
          </div>
        </div>
      </main>

      {/* 底部导航栏 */}
      <footer className={`sticky bottom-0 border-t transition-colors duration-300 ${
        darkMode 
          ? 'bg-gray-800 border-gray-600' 
          : 'bg-white border-gray-200'
      }`}>
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* 上一章按钮 */}
            <button
              onClick={() => {
                if (hasPrevious) {
                  const prevChapter = allChapters[currentIndex - 1];
                  // 这里应该触发父组件更新当前章节
                  // 暂时用location.reload来模拟，实际需要回调函数
                  console.log('Navigate to previous chapter:', prevChapter);
                }
              }}
              disabled={!hasPrevious}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                !hasPrevious
                  ? darkMode 
                    ? 'text-gray-600 cursor-not-allowed' 
                    : 'text-gray-400 cursor-not-allowed'
                  : darkMode
                    ? 'text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span>上一章</span>
            </button>

            {/* 中间：章节导航信息 */}
            <div className="text-center">
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {currentChapter.request_data.chapter_number} / {allChapters.length}
              </div>
              <div className={`text-xs mt-1 ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                使用 {getProviderDisplayName(selectedAIProvider)} 生成
              </div>
            </div>

            {/* 下一章/生成按钮 */}
            {hasNext ? (
              <button
                onClick={() => {
                  const nextChapter = allChapters[currentIndex + 1];
                  console.log('Navigate to next chapter:', nextChapter);
                }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  darkMode
                    ? 'text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <span>下一章</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            ) : (
              <button
                onClick={onGenerateNextChapter}
                disabled={isGenerating}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  isGenerating
                    ? darkMode
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : darkMode
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>生成中...</span>
                  </>
                ) : (
                  <>
                    <span>生成下一章</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </footer>
    </div>
  );
};