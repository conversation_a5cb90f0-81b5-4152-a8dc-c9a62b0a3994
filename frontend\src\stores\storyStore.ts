import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// 故事数据类型定义 - 与后端API响应格式匹配
export interface StoryBible {
  id: string;  // 修改为string以匹配后端UUID格式
  title: string;
  genre?: string;  // 添加类型字段
  theme?: string;  // 添加主题字段
  protagonist?: string;  // 添加主角字段
  setting?: string;  // 添加背景字段
  plot_outline?: string;  // 添加情节大纲字段
  concept?: string;  // 保留原有概念字段用于兼容
  content?: string;  // 生成的内容
  character_profiles?: string;  // 角色档案
  world_building?: string;  // 世界观设定
  plot_structure?: string;  // 情节结构
  writing_guidelines?: string;  // 写作指南
  model?: string;  // AI模型
  usage?: any;  // 使用统计
  created_at: string;
  updated_at?: string;
}

export interface Chapter {
  id: string;  // 修改为string以匹配后端UUID格式
  story_bible_id: string;  // 修改为string以匹配后端UUID格式
  chapter_number: number;
  title?: string;
  outline?: string;  // 章节大纲
  content: string;  // 章节内容
  summary?: string;  // 章节摘要
  character_development_notes?: string;  // 角色发展记录
  plot_advancement?: string;  // 情节推进记录
  model?: string;  // AI模型
  usage?: any;  // 使用统计
  created_at: string;
  updated_at?: string;
}

// 故事状态接口
interface StoryState {
  // 当前状态
  userPrompt: string;
  storyBible: StoryBible | null;
  chapters: Chapter[];
  currentChapter: Chapter | null;
  selectedAIProvider: string | null;
  
  // UI状态
  isGenerating: boolean;
  error: string | null;
  
  // 编辑状态
  currentEditingChapter: string | null;  // 修改为string类型
  hasUnsavedChanges: boolean;
  lastSavedAt: string | null;

  // 历史记录
  history: {
    action: string;
    timestamp: string;
    data?: any;
  }[];

  // Actions
  setUserPrompt: (prompt: string) => void;
  setStoryBible: (bible: StoryBible | null) => void;
  addChapter: (chapter: Chapter) => void;
  updateChapter: (chapterId: string, updates: Partial<Chapter>) => void;  // 修改为string类型
  removeChapter: (chapterId: string) => void;  // 修改为string类型
  setChapters: (chapters: Chapter[]) => void;
  setCurrentChapter: (chapter: Chapter | null) => void;
  setSelectedAIProvider: (provider: string | null) => void;
  setGenerating: (generating: boolean) => void;
  setError: (error: string | null) => void;

  // 编辑相关
  setCurrentEditingChapter: (chapterId: string | null) => void;  // 修改为string类型
  markSaved: () => void;
  markUnsaved: () => void;
  
  // 历史记录
  addToHistory: (action: string, data?: any) => void;
  clearHistory: () => void;
  
  // 数据管理
  exportStoryData: () => string;
  importStoryData: (data: string) => boolean;
  
  // 重置状态
  resetStory: () => void;
  resetAll: () => void;
}

// 创建故事状态存储
export const useStoryStore = create<StoryState>()(
  persist(
    immer((set, get) => ({
      // 初始状态
      userPrompt: '我想写一个小明（2024年的人）穿越到大明，帮助朱元璋从乞丐的时候一步一步统治了世界。',
      storyBible: null,
      chapters: [],
      currentChapter: null,
      selectedAIProvider: null,
      isGenerating: false,
      error: null,
      currentEditingChapter: null,
      hasUnsavedChanges: false,
      lastSavedAt: null,
      history: [],

      // Actions
      setUserPrompt: (prompt: string) => {
        set((state) => {
          console.log('📖 [StoryStore] 设置用户输入:', prompt.substring(0, 50) + '...');
          state.userPrompt = prompt;
          state.addToHistory('设置用户提示', { prompt: prompt.substring(0, 100) });
        });
      },

      setStoryBible: (bible: StoryBible | null) => {
        set((state) => {
          console.log('📖 [StoryStore] 设置故事圣经:', bible ? `ID:${bible.id} 标题:${bible.title}` : '清空');
          state.storyBible = bible;
          state.addToHistory('设置故事圣经', bible ? { id: bible.id, title: bible.title } : null);
        });
      },

      addChapter: (chapter: Chapter) => {
        set((state) => {
          console.log('📖 [StoryStore] 添加新章节:', `第${chapter.chapter_number}章`);
          state.chapters.push(chapter);
          state.currentChapter = chapter;
          state.addToHistory('添加章节', { 
            id: chapter.id, 
            chapterNumber: chapter.chapter_number,
            title: chapter.title 
          });
        });
      },

      updateChapter: (chapterId: string, updates: Partial<Chapter>) => {
        set((state) => {
          const chapterIndex = state.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            console.log('📖 [StoryStore] 更新章节:', `ID:${chapterId}`);
            Object.assign(state.chapters[chapterIndex], updates);

            // 如果更新的是当前章节，同步更新
            if (state.currentChapter?.id === chapterId) {
              Object.assign(state.currentChapter, updates);
            }

            state.hasUnsavedChanges = true;
            state.addToHistory('更新章节', { chapterId, updates: Object.keys(updates) });
          }
        });
      },

      removeChapter: (chapterId: string) => {
        set((state) => {
          const chapterIndex = state.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            const removedChapter = state.chapters[chapterIndex];
            console.log('📖 [StoryStore] 删除章节:', `第${removedChapter.chapter_number}章`);

            state.chapters.splice(chapterIndex, 1);

            // 如果删除的是当前章节，清空当前章节
            if (state.currentChapter?.id === chapterId) {
              state.currentChapter = null;
            }

            state.addToHistory('删除章节', {
              id: chapterId,
              chapterNumber: removedChapter.chapter_number
            });
          }
        });
      },

      setChapters: (chapters: Chapter[]) => {
        set((state) => {
          console.log('📖 [StoryStore] 设置章节列表:', `共${chapters.length}章`);
          state.chapters = chapters;
          state.addToHistory('设置章节列表', { count: chapters.length });
        });
      },

      setCurrentChapter: (chapter: Chapter | null) => {
        set((state) => {
          console.log('📖 [StoryStore] 设置当前章节:', chapter ? `第${chapter.chapter_number}章` : '清空');
          state.currentChapter = chapter;
        });
      },

      setSelectedAIProvider: (provider: string | null) => {
        set((state) => {
          console.log('📖 [StoryStore] 设置AI提供商:', provider || '默认');
          state.selectedAIProvider = provider;
          state.addToHistory('设置AI提供商', { provider });
        });
      },

      setGenerating: (generating: boolean) => {
        set((state) => {
          console.log('📖 [StoryStore] 设置生成状态:', generating ? '生成中' : '空闲');
          state.isGenerating = generating;
        });
      },

      setError: (error: string | null) => {
        set((state) => {
          if (error) {
            console.error('📖 [StoryStore] 设置错误:', error);
            state.addToHistory('发生错误', { error });
          } else {
            console.log('📖 [StoryStore] 清除错误');
          }
          state.error = error;
        });
      },

      // 编辑相关
      setCurrentEditingChapter: (chapterId: string | null) => {
        set((state) => {
          console.log('📖 [StoryStore] 设置编辑章节:', chapterId ? `ID:${chapterId}` : '无');
          state.currentEditingChapter = chapterId;
        });
      },

      markSaved: () => {
        set((state) => {
          console.log('📖 [StoryStore] 标记已保存');
          state.hasUnsavedChanges = false;
          state.lastSavedAt = new Date().toISOString();
        });
      },

      markUnsaved: () => {
        set((state) => {
          state.hasUnsavedChanges = true;
        });
      },

      // 历史记录
      addToHistory: (action: string, data?: any) => {
        set((state) => {
          const historyItem = {
            action,
            timestamp: new Date().toISOString(),
            data
          };
          state.history.push(historyItem);
          
          // 保持历史记录在100条以内
          if (state.history.length > 100) {
            state.history.splice(0, state.history.length - 100);
          }
        });
      },

      clearHistory: () => {
        set((state) => {
          console.log('📖 [StoryStore] 清除历史记录');
          state.history = [];
        });
      },

      // 数据管理
      exportStoryData: () => {
        const state = get();
        const exportData = {
          userPrompt: state.userPrompt,
          storyBible: state.storyBible,
          chapters: state.chapters,
          selectedAIProvider: state.selectedAIProvider,
          exportedAt: new Date().toISOString()
        };
        console.log('📖 [StoryStore] 导出故事数据');
        return JSON.stringify(exportData, null, 2);
      },

      importStoryData: (data: string) => {
        try {
          const importData = JSON.parse(data);
          set((state) => {
            console.log('📖 [StoryStore] 导入故事数据');
            if (importData.userPrompt) state.userPrompt = importData.userPrompt;
            if (importData.storyBible) state.storyBible = importData.storyBible;
            if (importData.chapters) state.chapters = importData.chapters;
            if (importData.selectedAIProvider) state.selectedAIProvider = importData.selectedAIProvider;
            state.addToHistory('导入数据', { 
              chaptersCount: importData.chapters?.length || 0 
            });
          });
          return true;
        } catch (error) {
          console.error('📖 [StoryStore] 导入数据失败:', error);
          set((state) => {
            state.error = '导入数据格式错误';
          });
          return false;
        }
      },

      resetStory: () => {
        set((state) => {
          console.log('📖 [StoryStore] 重置故事状态');
          state.storyBible = null;
          state.chapters = [];
          state.currentChapter = null;
          state.isGenerating = false;
          state.error = null;
          state.currentEditingChapter = null;
          state.hasUnsavedChanges = false;
          state.addToHistory('重置故事');
        });
      },

      resetAll: () => {
        set((state) => {
          console.log('📖 [StoryStore] 重置所有状态');
          state.userPrompt = '我想写一个小明（2024年的人）穿越到大明，帮助朱元璋从乞丐的时候一步一步统治了世界。';
          state.storyBible = null;
          state.chapters = [];
          state.currentChapter = null;
          state.selectedAIProvider = null;
          state.isGenerating = false;
          state.error = null;
          state.currentEditingChapter = null;
          state.hasUnsavedChanges = false;
          state.lastSavedAt = null;
          state.history = [];
        });
      }
    })),
    {
      name: 'story-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        userPrompt: state.userPrompt,
        storyBible: state.storyBible,
        chapters: state.chapters,
        selectedAIProvider: state.selectedAIProvider,
        lastSavedAt: state.lastSavedAt,
        history: state.history.slice(-20) // 只持久化最近20条历史记录
      }),
      version: 1,
      migrate: (persistedState: any, version: number) => {
        console.log('📖 [StoryStore] 迁移存储版本:', version);
        // 在这里处理版本迁移逻辑
        return persistedState;
      }
    }
  )
);