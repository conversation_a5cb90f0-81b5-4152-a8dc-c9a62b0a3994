import axios from 'axios';
import { API_ENDPOINTS } from '../../constants/api';
import { debugLog } from '../../config/env';
import type {
  // 后端类型导入
  StoryBibleRequest,
  StoryBibleResponse,
  ChapterGenerationRequest,
  ChapterResponse,
  TaskStatusResponse,
  TaskListResponse,
  ChapterListResponse,
  HealthCheckResponse,
  RootResponse,
  PaginationParams,
  FilterParams} from '../../types/backend';
import { AIProvider, StoryGenre } from '../../types/backend';

/**
 * 简单的API服务类
 * 提供与后端交互的所有API方法
 */
export class SimpleApiService {
  // 固定的开发用token
  static readonly DEV_TOKEN = 'dev-fixed-token-2024';
  
  /**
   * 🔧 [调试] 健康检查API - 与后端/health端点匹配
   */
  static async healthCheck(): Promise<{ connected: boolean, latency?: number }> {
    try {
      const startTime = performance.now();
      const response = await axios.get<HealthCheckResponse>(API_ENDPOINTS.HEALTH);
      const endTime = performance.now();
      const latency = Math.round(endTime - startTime);

      debugLog('系统', '健康检查成功', {
        status: response.data.status,
        service: response.data.service,
        version: response.data.version,
        latency: `${latency}ms`,
        ai_providers: response.data.ai_providers
      });

      return {
        connected: response.status === 200 && response.data.status === 'healthy',
        latency: latency
      };
    } catch (error) {
      debugLog('错误', '健康检查失败', { error });
      return { connected: false };
    }
  }

  /**
   * 🎨 [UI] 获取根路径信息 - 与后端/端点匹配
   */
  static async getRootInfo(): Promise<RootResponse | null> {
    try {
      const response = await axios.get<RootResponse>(API_ENDPOINTS.ROOT);
      debugLog('系统', '获取根路径信息成功', response.data);
      return response.data;
    } catch (error) {
      debugLog('错误', '获取根路径信息失败', { error });
      return null;
    }
  }

  /**
   * 🔐 [认证] 开发环境模拟登录 - 后端暂无认证系统
   */
  static async loginWithFixedToken(): Promise<{ success: boolean, token?: string, message: string }> {
    try {
      debugLog('认证', '开始模拟登录流程');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      debugLog('认证', '模拟登录成功', { token: this.DEV_TOKEN });

      return {
        success: true,
        token: this.DEV_TOKEN,
        message: '开发环境模拟登录成功。后端暂无认证系统，使用固定token。'
      };
    } catch (error) {
      debugLog('错误', '模拟登录失败', { error });
      return {
        success: false,
        message: '模拟登录失败'
      };
    }
  }

  /**
   * 🔐 [认证] 验证token有效性 - 开发环境模拟
   */
  static async validateToken(token: string): Promise<boolean> {
    try {
      debugLog('认证', '验证token', { token: token.substring(0, 10) + '...' });

      // 开发环境简单验证
      const isValid = token === this.DEV_TOKEN;
      debugLog('认证', 'Token验证结果', { valid: isValid });

      return isValid;
    } catch (error) {
      debugLog('错误', 'Token验证失败', { error });
      return false;
    }
  }

  /**
   * 📝 [生成] 生成故事圣经 - 与后端/api/v1/generate-bible端点匹配
   */
  static async generateStoryBible(request: StoryBibleRequest): Promise<StoryBibleResponse> {
    try {
      debugLog('生成', '开始生成故事圣经', {
        title: request.title,
        genre: request.genre,
        ai_provider: request.ai_provider
      });

      const response = await axios.post<StoryBibleResponse>(
        API_ENDPOINTS.STORY.GENERATE_BIBLE,
        request,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      debugLog('生成', '故事圣经生成任务已创建', {
        task_id: response.data.id,
        status: response.data.status,
        task_type: response.data.task_type
      });

      return response.data;
    } catch (error) {
      debugLog('错误', '故事圣经生成失败', { error });
      throw error;
    }
  }

  /**
   * 📝 [生成] 生成故事圣经（直接返回后端格式）
   */
  static async generateStoryBibleLegacy(concept: string, aiProvider?: string): Promise<StoryBibleResponse> {
    try {
      // 构造符合后端要求的请求
      const request: StoryBibleRequest = {
        title: "AI生成小说", // 默认标题
        genre: StoryGenre.FANTASY, // 默认类型
        theme: concept,
        protagonist: "待定主角",
        setting: "待定背景",
        plot_outline: concept,
        ai_provider: (aiProvider as AIProvider) || AIProvider.ZHIPU,
        temperature: 0.8,
        max_tokens: 3000
      };

      const response = await this.generateStoryBible(request);

      // 直接返回后端响应格式
      return response;
    } catch (error) {
      debugLog('错误', '故事圣经生成失败', { error });
      throw error;
    }
  }

  /**
   * 📋 [任务] 查询任务状态 - 与后端/api/v1/tasks/{task_id}/status端点匹配
   */
  static async getTaskStatus(taskId: string): Promise<TaskStatusResponse | null> {
    try {
      debugLog('任务', '查询任务状态', { task_id: taskId });

      const response = await axios.get<TaskStatusResponse>(
        API_ENDPOINTS.STORY.GET_TASK_STATUS(taskId)
      );

      debugLog('任务', '任务状态查询成功', {
        task_id: response.data.id,
        status: response.data.status,
        progress: response.data.progress
      });

      return response.data;
    } catch (error) {
      debugLog('错误', '任务状态查询失败', { task_id: taskId, error });
      return null;
    }
  }

  /**
   * 📋 [任务] 获取任务列表 - 与后端/api/v1/tasks端点匹配
   */
  static async listTasks(params?: PaginationParams & FilterParams): Promise<TaskListResponse | null> {
    try {
      debugLog('任务', '获取任务列表', params);

      const response = await axios.get<TaskListResponse>(
        API_ENDPOINTS.STORY.LIST_TASKS,
        { params }
      );

      debugLog('任务', '任务列表获取成功', {
        total: response.data.total,
        count: response.data.tasks.length
      });

      return response.data;
    } catch (error) {
      debugLog('错误', '任务列表获取失败', { error });
      return null;
    }
  }

  /**
   * 📝 [生成] 生成故事圣经（流式）- 暂时保留旧实现
   */
  static async generateStoryBibleStream(
    concept: string,
    aiProvider: string | null,
    onChunk: (chunk: string) => void,
    onComplete: (data: StoryBibleResponse) => void,
    onError: (error: string) => void
  ): Promise<void> {
    console.log('📚 [API流式生成] 生成故事圣经，参数:', concept, 'AI提供商:', aiProvider);
    
    try {
      const requestBody: any = {
        story_concept: concept,
        temperature: 0.8,
        max_tokens: 3000,
        stream: true  // 流式模式
      };
      
      // 如果指定了AI提供商，添加到请求中
      if (aiProvider) {
        requestBody.ai_provider = aiProvider;
      }
      
      const response = await fetch(API_ENDPOINTS.STORY.GENERATE_BIBLE, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          console.log('📚 [API流式生成] 流式响应结束');
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.slice(6);
            if (dataStr === '[DONE]') {
              console.log('📚 [API流式生成] 收到结束信号');
              return;
            }

            try {
              const data = JSON.parse(dataStr);
              console.log('📚 [API流式生成] 解析数据:', data);
              
              switch (data.type) {
                case 'start':
                  console.log('📚 [API流式生成] 开始生成:', data.message);
                  break;
                case 'content':
                  onChunk(data.data);
                  break;
                case 'complete':
                  console.log('📚 [API流式生成] 生成完成:', data.data);
                  onComplete(data.data);
                  return;
                case 'error':
                  console.error('📚 [API流式生成] 生成错误:', data.message);
                  onError(data.message);
                  return;
              }
            } catch (parseError) {
              console.error('📚 [API流式生成] 解析数据失败:', parseError, '原始数据:', dataStr);
            }
          }
        }
      }
    } catch (error) {
      console.error('📚 [API流式生成] 流式生成失败:', error);
      onError(error instanceof Error ? error.message : '网络请求失败');
    }
  }

  /**
   * 📝 [生成] 生成章节内容 - 与后端/api/v1/generate-chapter端点匹配
   */
  static async generateChapter(request: ChapterGenerationRequest): Promise<ChapterResponse> {
    try {
      debugLog('生成', '开始生成章节', {
        story_bible_id: request.story_bible_id,
        chapter_number: request.chapter_number,
        chapter_title: request.chapter_title,
        ai_provider: request.ai_provider
      });

      const response = await axios.post<ChapterResponse>(
        API_ENDPOINTS.STORY.GENERATE_CHAPTER,
        request,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      debugLog('生成', '章节生成任务已创建', {
        task_id: response.data.id,
        status: response.data.status,
        chapter_number: response.data.request_data.chapter_number
      });

      return response.data;
    } catch (error) {
      debugLog('错误', '章节生成失败', { error });
      throw error;
    }
  }

  /**
   * 📝 [生成] 生成章节内容（直接返回后端格式）
   */
  static async generateChapterLegacy(
    storyBibleId: string,
    chapterNumber: number,
    outline: string,
    aiProvider?: string
  ): Promise<ChapterResponse> {
    try {
      // 构造符合后端要求的请求
      const request: ChapterGenerationRequest = {
        story_bible_id: storyBibleId,
        chapter_number: chapterNumber,
        chapter_title: `第${chapterNumber}章`,
        chapter_outline: outline,
        target_word_count: 2000,
        ai_provider: (aiProvider as AIProvider) || AIProvider.ZHIPU,
        temperature: 0.7,
        max_tokens: 4000
      };

      const response = await this.generateChapter(request);

      // 直接返回后端响应格式
      return response;
    } catch (error) {
      debugLog('错误', '章节生成失败', { error });
      throw error;
    }
  }

  /**
   * 📁 [文件] 获取故事圣经的章节列表 - 与后端/api/v1/bible/{bible_id}/chapters端点匹配
   */
  static async getBibleChapters(
    bibleId: string,
    params?: PaginationParams & FilterParams
  ): Promise<ChapterListResponse | null> {
    try {
      debugLog('文件', '获取章节列表', { bible_id: bibleId, ...params });

      const response = await axios.get<ChapterListResponse>(
        API_ENDPOINTS.STORY.GET_BIBLE_CHAPTERS(bibleId),
        { params }
      );

      debugLog('文件', '章节列表获取成功', {
        bible_id: bibleId,
        total: response.data.total,
        count: response.data.chapters.length
      });

      return response.data;
    } catch (error) {
      debugLog('错误', '章节列表获取失败', { bible_id: bibleId, error });
      return null;
    }
  }

  /**
   * 📁 [文件] 获取故事的所有章节（直接返回后端格式）
   */
  static async getChapters(storyBibleId: string): Promise<ChapterResponse[]> {
    try {
      const response = await this.getBibleChapters(storyBibleId);

      if (!response) {
        return [];
      }

      // 直接返回后端响应格式
      return response.chapters;
    } catch (error) {
      debugLog('错误', '获取章节失败', { story_bible_id: storyBibleId, error });
      return [];
    }
  }
}